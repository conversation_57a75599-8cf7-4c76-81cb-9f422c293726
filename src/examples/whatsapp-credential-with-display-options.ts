// Example WhatsApp credential type with display_options for conditional field visibility
// This demonstrates how fields can be shown/hidden based on other field values

export const whatsappCredentialWithDisplayOptions = {
  name: "whatsapp_api",
  display_name: "WhatsApp Business",
  description: "WhatsApp Business API credentials with conditional fields",
  icon: "📱",
  icon_color: "#25D366",
  icon_url: "",
  documentation_url: "https://developers.facebook.com/docs/whatsapp",
  subtitle: "Connect to WhatsApp Business API",
  version: 1.0,
  parameters: [
    {
      name: "access_token",
      type: "string",
      display_name: "Access Token",
      description: "Your WhatsApp Business API access token",
      required: true,
      sensitive: true,
      placeholder: "Enter your access token...",
    },
    {
      name: "phone_number_id",
      type: "string",
      display_name: "Phone Number ID",
      description: "The WhatsApp Business phone number ID",
      required: true,
      placeholder: "1234567890123456",
    },
    {
      name: "connection_type",
      type: "options",
      display_name: "Connection Type",
      description: "Choose how to connect to WhatsApp API",
      required: true,
      default: "cloud_api",
      options: [
        { name: "Cloud API", value: "cloud_api", description: "Use Facebook's Cloud API" },
        { name: "On-Premises", value: "on_premises", description: "Use on-premises WhatsApp Business API" },
        { name: "Custom", value: "custom", description: "Custom API endpoint" },
      ],
    },
    // This field only shows when connection_type is cloud_api or on_premises
    {
      name: "webhook_verify_token",
      type: "string",
      display_name: "Webhook Verify Token",
      description: "Token used to verify webhook requests",
      required: false,
      sensitive: true,
      placeholder: "Enter webhook verify token...",
      display_options: {
        show: {
          connection_type: ["cloud_api", "on_premises"]
        }
      }
    },
    // This field only shows when connection_type is cloud_api
    {
      name: "api_version",
      type: "options",
      display_name: "API Version",
      description: "WhatsApp Business API version to use",
      required: true,
      default: "v17.0",
      options: [
        { name: "v17.0", value: "v17.0", description: "Latest stable version" },
        { name: "v16.0", value: "v16.0", description: "Previous version" },
        { name: "v15.0", value: "v15.0", description: "Legacy version" },
      ],
      display_options: {
        show: {
          connection_type: ["cloud_api"]
        }
      }
    },
    // This field only shows when connection_type is cloud_api
    {
      name: "base_url",
      type: "string",
      display_name: "Base URL",
      description: "WhatsApp Business API base URL",
      required: false,
      default: "https://graph.facebook.com",
      placeholder: "https://graph.facebook.com",
      display_options: {
        show: {
          connection_type: ["cloud_api"]
        }
      }
    },
    // This field only shows when connection_type is custom
    {
      name: "custom_endpoint",
      type: "string",
      display_name: "Custom API Endpoint",
      description: "Your custom WhatsApp API endpoint URL",
      required: true,
      placeholder: "https://your-api.example.com",
      display_options: {
        show: {
          connection_type: ["custom"]
        }
      }
    },
    // These fields only show when connection_type is on_premises
    {
      name: "on_premises_host",
      type: "string",
      display_name: "On-Premises Host",
      description: "Your on-premises WhatsApp Business API host",
      required: true,
      placeholder: "your-whatsapp-host.com",
      display_options: {
        show: {
          connection_type: ["on_premises"]
        }
      }
    },
    {
      name: "on_premises_port",
      type: "number",
      display_name: "On-Premises Port",
      description: "Port number for your on-premises API",
      required: false,
      default: 9090,
      placeholder: "9090",
      display_options: {
        show: {
          connection_type: ["on_premises"]
        }
      }
    },
  ],
  allowed_nodes: ["whatsapp"],
  base_url: "https://graph.facebook.com",
};

/*
How this works in the CredentialModal:

1. Initially, only these fields are visible:
   - Access Token (always visible)
   - Phone Number ID (always visible)  
   - Connection Type (always visible)

2. When user selects "Cloud API":
   - Webhook Verify Token becomes visible
   - API Version becomes visible
   - Base URL becomes visible

3. When user selects "On-Premises":
   - Webhook Verify Token becomes visible
   - On-Premises Host becomes visible
   - On-Premises Port becomes visible

4. When user selects "Custom":
   - Custom API Endpoint becomes visible

This provides a clean, dynamic form that only shows relevant fields based on the user's choices.
*/
